var Sequelize = require('sequelize')
var user = require('./user')
var bar = require('./bar')
var segment = require('./segment')
var ads = require('./ads')

var env = require('../config/environment')

const {sequelize} = require('../config/db').db;

var ads_users = sequelize.define(
    'ads_users',
    {
        id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true
        },
        userID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'user',
                key: 'id'
            }
        },
        adsID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'ads',
                key: 'id'
            }
        },
        barID: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
                model: 'bar',
                key: 'id'
            }
        }
    },
    {
        freezeTableName: true,
        timestamps: false
    }
)


ads_users.belongsTo(ads, { foreignKey: 'adsID' });
ads_users.belongsTo(user, {
                foreignKey: 'userID'
            });
module.exports = ads_users